import { Routes } from '@angular/router';
import { Login } from './auth/login/login';
import { Register } from './auth/register/register';
import { AuthGuard } from './auth/auth-guard';

export const routes: Routes = [
    // Default route redirects to login
    { path: '', redirectTo: '/auth/login', pathMatch: 'full' },

    // Authentication routes (public)
    {
        path: 'auth',
        children: [
            { path: 'login', component: Login },
            { path: 'register', component: Register }
        ]
    },

    // Protected routes (require authentication) - Temporarily commented out until dashboards are created
    // {
    //     path: 'admin',
    //     canActivate: [AuthGuard],
    //     children: [
    //         {
    //             path: 'dashboard',
    //             loadComponent: () => import('./dashboards/admin-dashboard/admin-dashboard').then(m => m.AdminDashboard)
    //         }
    //     ]
    // },
    // {
    //     path: 'broker',
    //     canActivate: [AuthGuard],
    //     children: [
    //         {
    //             path: 'dashboard',
    //             loadComponent: () => import('./dashboards/broker-dashboard/broker-dashboard').then(m => m.BrokerDashboard)
    //         }
    //     ]
    // },
    // {
    //     path: 'client',
    //     canActivate: [AuthGuard],
    //     children: [
    //         {
    //             path: 'dashboard',
    //             loadComponent: () => import('./dashboards/client-dashboard/client-dashboard').then(m => m.ClientDashboard)
    //         }
    //     ]
    // },

    // Wildcard route - must be last
    { path: '**', redirectTo: '/auth/login' }
];
