<div class="register-container">
    <div class="auth-layout">
        <!-- Register Form Section -->
        <div class="register-card">
            <div class="register-header">
                <h1 class="register-title">Create Account</h1>
                <p class="register-subtitle">Join <PERSON> to manage your life insurance</p>
            </div>

            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
                <!-- Name Field -->
                <div class="form-group">
                    <label for="name" class="form-label">Full Name</label>
                    <input type="text" id="name" formControlName="name" class="form-input"
                        [class.error]="isFieldInvalid('name')" placeholder="Enter your full name" />
                    <div class="error-message" *ngIf="isFieldInvalid('name')">
                        <span *ngIf="registerForm.get('name')?.errors?.['required']">Name is required</span>
                        <span *ngIf="registerForm.get('name')?.errors?.['minlength']">Name must be at least 2
                            characters</span>
                    </div>
                </div>

                <!-- Email Field -->
                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" id="email" formControlName="email" class="form-input"
                        [class.error]="isFieldInvalid('email')" placeholder="Enter your email" />
                    <div class="error-message" *ngIf="isFieldInvalid('email')">
                        <span *ngIf="registerForm.get('email')?.errors?.['required']">Email is required</span>
                        <span *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email</span>
                    </div>
                </div>

                <!-- Role Selection -->
                <div class="form-group">
                    <label for="role" class="form-label">Account Type</label>
                    <select id="role" formControlName="role" class="form-input form-select"
                        [class.error]="isFieldInvalid('role')">
                        <option value="">Select your role</option>
                        <option value="client">Client - I want to purchase insurance</option>
                        <option value="broker">Broker - I sell insurance products</option>
                    </select>
                    <div class="error-message" *ngIf="isFieldInvalid('role')">
                        <span *ngIf="registerForm.get('role')?.errors?.['required']">Please select your account
                            type</span>
                    </div>
                </div>

                <!-- Password Field -->
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="password-input-container">
                        <input [type]="showPassword ? 'text' : 'password'" id="password" formControlName="password"
                            class="form-input" [class.error]="isFieldInvalid('password')"
                            placeholder="Create a password" />
                        <button type="button" class="password-toggle" (click)="togglePasswordVisibility()">
                            <span class="password-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</span>
                        </button>
                    </div>
                    <div class="error-message" *ngIf="isFieldInvalid('password')">
                        <span *ngIf="registerForm.get('password')?.errors?.['required']">Password is required</span>
                        <span *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 6
                            characters</span>
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <div class="password-input-container">
                        <input [type]="showConfirmPassword ? 'text' : 'password'" id="confirmPassword"
                            formControlName="confirmPassword" class="form-input"
                            [class.error]="isFieldInvalid('confirmPassword')" placeholder="Confirm your password" />
                        <button type="button" class="password-toggle" (click)="toggleConfirmPasswordVisibility()">
                            <span class="password-icon">{{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}</span>
                        </button>
                    </div>
                    <div class="error-message" *ngIf="isFieldInvalid('confirmPassword')">
                        <span *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Please confirm your
                            password</span>
                        <span *ngIf="registerForm.get('confirmPassword')?.errors?.['passwordMismatch']">Passwords do not
                            match</span>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" formControlName="acceptTerms" class="checkbox-input">
                        <span class="checkbox-custom"></span>
                        I agree to the <a href="#" class="link">Terms of Service</a> and <a href="#"
                            class="link">Privacy
                            Policy</a>
                    </label>
                    <div class="error-message" *ngIf="isFieldInvalid('acceptTerms')">
                        <span *ngIf="registerForm.get('acceptTerms')?.errors?.['required']">You must accept the terms
                            and
                            conditions</span>
                    </div>
                </div>

                <!-- Error Message -->
                <div class="error-message global-error" *ngIf="errorMessage">
                    {{ errorMessage }}
                </div>

                <!-- Submit Button -->
                <button type="submit" class="submit-btn" [disabled]="registerForm.invalid || isLoading">
                    <span *ngIf="!isLoading">Create Account</span>
                    <span *ngIf="isLoading" class="loading-spinner">
                        <span class="spinner"></span>
                        Creating account...
                    </span>
                </button>
            </form>

            <div class="register-footer">
                <p class="login-link">
                    Already have an account?
                    <a (click)="navigateToLogin()" class="link">Sign in here</a>
                </p>
            </div>
        </div>

        <!-- Image Slider Sidebar -->
        <div class="image-slider-sidebar">
            <div class="slider-container">
                <div class="slider-wrapper">
                    <div class="slide" [class.active]="currentSlide === 0">
                        <img src="images/Colllateral-img1.jpg" alt="Collateral Management" class="slide-image">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Collateral Management</h3>
                            <p class="slide-subtitle">Advanced Solutions</p>
                        </div>
                    </div>

                    <div class="slide" [class.active]="currentSlide === 1">
                        <img src="images/Healthcare-and-protection.jpg" alt="Healthcare Protection" class="slide-image">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Healthcare & Protection</h3>
                            <p class="slide-subtitle">Comprehensive Coverage</p>
                        </div>
                    </div>

                    <div class="slide" [class.active]="currentSlide === 2">
                        <img src="images/high-rising-buildings-toronto-canada-1.png" alt="Financial Excellence"
                            class="slide-image">
                        <div class="slide-overlay">
                            <h3 class="slide-title">Financial Excellence</h3>
                            <p class="slide-subtitle">Building Success</p>
                        </div>
                    </div>
                </div>

                <!-- Slide Indicators -->
                <div class="slide-indicators">
                    <button *ngFor="let slide of slides; let i = index" class="indicator"
                        [class.active]="currentSlide === i" (click)="goToSlide(i)">
                    </button>
                </div>
            </div>
        </div>
    </div>