{"version": 3, "file": "zu.js", "sourceRoot": "", "sources": ["zu.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,aAAa,EAAC,YAAY,EAAC,cAAc,EAAC,UAAU,EAAC,aAAa,EAAC,WAAW,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,sBAAsB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"zu\",[[\"a\",\"p\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"S\",\"M\",\"B\",\"T\",\"S\",\"H\",\"M\"],[\"Son\",\"<PERSON>o\",\"<PERSON><PERSON>\",\"<PERSON>ha\",\"Sin\",\"Hla\",\"Mgq\"],[\"ISonto\",\"UMsombulu<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"ULwesithathu\",\"ULwesine\",\"ULwesihlanu\",\"UMgqibelo\"],[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>la\",\"Mgq\"]],u,[[\"J\",\"F\",\"M\",\"E\",\"M\",\"J\",\"J\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>y\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>t\",\"<PERSON>\",\"<PERSON><PERSON>\"],[\"<PERSON>u<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>yi\",\"<PERSON>i\",\"<PERSON><PERSON>i\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>ba\",\"Okthoba\",\"Novemba\",\"Disemba\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"J\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"Jan\",\"Feb\",\"Mas\",\"Eph\",\"Mey\",\"Jun\",\"Jul\",\"Aga\",\"Sep\",\"Okt\",\"Nov\",\"Dis\"],[\"Januwari\",\"Februwari\",\"Mashi\",\"Ephreli\",\"Meyi\",\"Juni\",\"Julayi\",\"Agasti\",\"Septhemba\",\"Okthoba\",\"Novemba\",\"Disemba\"]],[[\"BC\",\"AD\"],u,u],0,[6,0],[\"M/d/yy\",\"MMM d, y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"ZAR\",\"R\",\"i-South African Rand\",{\"BYN\":[u,\"P.\"],\"DKK\":[u,\"Kr\"],\"HRK\":[u,\"Kn\"],\"ISK\":[u,\"Kr\"],\"JPY\":[\"JP¥\",\"¥\"],\"NOK\":[u,\"Kr\"],\"PHP\":[u,\"₱\"],\"PLN\":[u,\"Zł\"],\"SEK\":[u,\"Kr\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"ZAR\":[\"R\"]},\"ltr\", plural];\n"]}