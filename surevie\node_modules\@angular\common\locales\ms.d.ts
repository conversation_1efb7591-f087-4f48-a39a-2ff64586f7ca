/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    CAD: (string | undefined)[];
    JPY: string[];
    MXN: (string | undefined)[];
    MYR: string[];
    PHP: (string | undefined)[];
    TWD: string[];
    USD: (string | undefined)[];
} | undefined)[];
export default _default;
