<header class="header">
  <div class="container">
    <div class="header-content">
      <!-- Logo -->
      <div class="logo">
        <a href="/" class="logo-link">
          <span class="logo-text">SureVie</span>
        </a>
      </div>

      <!-- Navigation Menu -->
      <nav class="nav-menu">
        <ul class="nav-list">
          <li class="nav-item">
            <a href="#" class="nav-link">Insurance</a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">Solutions</a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">About us</a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">Resources</a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">Careers</a>
          </li>
        </ul>
      </nav>

      <!-- Auth Actions -->
      <div class="auth-actions">
        <ng-container *ngIf="!isAuthenticated; else authenticatedUser">
          <button class="btn btn-outline" (click)="navigateToLogin()">Login</button>
          <button class="btn btn-primary" (click)="navigateToRegister()">Sign Up</button>
        </ng-container>

        <ng-template #authenticatedUser>
          <div class="user-menu">
            <span class="user-name">Welcome, {{currentUser?.name}}</span>
            <button class="btn btn-outline" (click)="logout()">Logout</button>
          </div>
        </ng-template>
      </div>

      <!-- Mobile Menu Toggle -->
      <button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" [class.active]="isMobileMenuOpen">
      <ul class="mobile-nav-list">
        <li class="mobile-nav-item">
          <a href="#" class="mobile-nav-link">Insurance</a>
        </li>
        <li class="mobile-nav-item">
          <a href="#" class="mobile-nav-link">Solutions</a>
        </li>
        <li class="mobile-nav-item">
          <a href="#" class="mobile-nav-link">About us</a>
        </li>
        <li class="mobile-nav-item">
          <a href="#" class="mobile-nav-link">Resources</a>
        </li>
        <li class="mobile-nav-item">
          <a href="#" class="mobile-nav-link">Careers</a>
        </li>
        <li class="mobile-nav-item auth-mobile">
          <ng-container *ngIf="!isAuthenticated">
            <button class="btn btn-outline mobile-btn" (click)="navigateToLogin()">Login</button>
            <button class="btn btn-primary mobile-btn" (click)="navigateToRegister()">Sign Up</button>
          </ng-container>
          <ng-container *ngIf="isAuthenticated">
            <button class="btn btn-outline mobile-btn" (click)="logout()">Logout</button>
          </ng-container>
        </li>
      </ul>
    </div>
  </div>
</header>
