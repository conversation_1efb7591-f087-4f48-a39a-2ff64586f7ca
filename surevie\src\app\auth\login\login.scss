// Vermeg-inspired color palette
$primary-red: #e31e24;
$dark-gray: #333333;
$light-gray: #f8f9fa;
$white: #ffffff;
$border-gray: #e0e0e0;
$error-red: #dc3545;
$success-green: #28a745;

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;

    .auth-layout {
        display: flex;
        max-width: 1200px;
        width: 100%;
        gap: 0;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        min-height: 600px;
    }

    .login-card {
        background: $white;
        flex: 1;
        max-width: 500px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 50px 60px;
        width: 100%;

        .login-header {
            text-align: center;
            margin-bottom: 50px;

            .login-title {
                font-size: 36px;
                font-weight: 700;
                color: $dark-gray;
                margin: 0 0 15px 0;
                font-family: 'Poppins', sans-serif;
                letter-spacing: -0.5px;
            }

            .login-subtitle {
                font-size: 18px;
                color: #666;
                margin: 0;
                font-family: 'Inter', sans-serif;
                font-weight: 400;
            }
        }

        .login-form {
            .form-group {
                margin-bottom: 30px;

                &.checkbox-group {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 25px;
                }

                .form-label {
                    display: block;
                    font-weight: 600;
                    color: $dark-gray;
                    margin-bottom: 12px;
                    font-size: 16px;
                    font-family: 'Inter', sans-serif;
                }

                .form-input {
                    width: 100%;
                    padding: 16px 20px;
                    border: 2px solid $border-gray;
                    border-radius: 12px;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    box-sizing: border-box;

                    &:focus {
                        outline: none;
                        border-color: $primary-red;
                        box-shadow: 0 0 0 3px rgba(227, 30, 36, 0.1);
                    }

                    &.error {
                        border-color: $error-red;

                        &:focus {
                            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
                        }
                    }

                    &::placeholder {
                        color: #999;
                    }
                }

                .password-input-container {
                    position: relative;

                    .password-toggle {
                        position: absolute;
                        right: 12px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: none;
                        border: none;
                        cursor: pointer;
                        padding: 4px;

                        .password-icon {
                            font-size: 18px;
                            color: #666;
                        }
                    }
                }

                .checkbox-label {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    font-size: 14px;
                    color: $dark-gray;

                    .checkbox-input {
                        display: none;

                        &:checked+.checkbox-custom {
                            background-color: $primary-red;
                            border-color: $primary-red;

                            &::after {
                                opacity: 1;
                            }
                        }
                    }

                    .checkbox-custom {
                        width: 18px;
                        height: 18px;
                        border: 2px solid $border-gray;
                        border-radius: 3px;
                        margin-right: 8px;
                        position: relative;
                        transition: all 0.3s ease;

                        &::after {
                            content: '✓';
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            color: $white;
                            font-size: 12px;
                            opacity: 0;
                            transition: opacity 0.3s ease;
                        }
                    }
                }

                .forgot-password {
                    color: $primary-red;
                    text-decoration: none;
                    font-size: 14px;
                    font-weight: 500;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                .error-message {
                    color: $error-red;
                    font-size: 13px;
                    margin-top: 5px;

                    &.global-error {
                        background-color: #f8d7da;
                        border: 1px solid #f5c6cb;
                        border-radius: 6px;
                        padding: 12px;
                        margin-bottom: 20px;
                        text-align: center;
                    }
                }
            }

            .submit-btn {
                width: 100%;
                background: linear-gradient(135deg, $primary-red 0%, darken($primary-red, 10%) 100%);
                color: $white;
                border: none;
                border-radius: 12px;
                padding: 18px 24px;
                font-size: 18px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-bottom: 30px;
                font-family: 'Inter', sans-serif;
                box-shadow: 0 4px 15px rgba(227, 30, 36, 0.3);

                &:hover:not(:disabled) {
                    background-color: darken($primary-red, 10%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(227, 30, 36, 0.3);
                }

                &:disabled {
                    background-color: #ccc;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                .loading-spinner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;

                    .spinner {
                        width: 16px;
                        height: 16px;
                        border: 2px solid transparent;
                        border-top: 2px solid $white;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                }
            }

            .demo-credentials {
                background-color: #f8f9fa;
                border: 1px solid $border-gray;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;

                .demo-title {
                    font-weight: 600;
                    color: $dark-gray;
                    margin: 0 0 10px 0;
                    font-size: 14px;
                }

                .demo-accounts {
                    .demo-account {
                        font-size: 13px;
                        color: #666;
                        margin-bottom: 5px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        strong {
                            color: $dark-gray;
                        }
                    }
                }
            }
        }

        .login-footer {
            text-align: center;

            .signup-link {
                color: #666;
                font-size: 14px;
                margin: 0;

                .link {
                    color: $primary-red;
                    text-decoration: none;
                    font-weight: 500;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// Image Slider Sidebar Styles
.image-slider-sidebar {
    flex: 1.3;
    max-width: 700px;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;

    .slider-container {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .slider-wrapper {
        position: relative;
        width: 100%;
        height: 500px;
        overflow: hidden;
    }

    .slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 0.8s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
            opacity: 1;
        }

        .slide-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: brightness(0.7);
        }

        .slide-overlay {
            position: absolute;
            bottom: 60px;
            left: 40px;
            right: 40px;
            color: white;
            z-index: 2;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
            padding: 30px 20px 20px;
            border-radius: 8px;

            .slide-title {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 8px;
                font-family: 'Poppins', sans-serif;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }

            .slide-subtitle {
                font-size: 16px;
                font-weight: 400;
                opacity: 0.95;
                font-family: 'Inter', sans-serif;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
            }
        }
    }

    .slide-indicators {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 12px;
        z-index: 3;

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid rgba(220, 53, 69, 0.6);
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: rgba(220, 53, 69, 0.8);
                transform: scale(1.1);
            }

            &.active {
                background: #dc3545;
                border-color: #dc3545;
                box-shadow: 0 0 10px rgba(220, 53, 69, 0.4);
            }
        }
    }
}

// Responsive design
@media (max-width: 1024px) {
    .login-container .auth-layout {
        flex-direction: column;
        max-width: 450px;
    }

    .login-card {
        padding: 40px 50px;
    }

    .image-slider-sidebar {
        max-width: none;
        min-height: 350px;

        .slider-wrapper {
            height: 350px;
        }

        .slide-overlay {
            bottom: 40px;
            left: 30px;
            right: 30px;

            .slide-title {
                font-size: 24px;
            }

            .slide-subtitle {
                font-size: 14px;
            }
        }
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 10px;

        .login-card {
            padding: 40px 25px;
        }
    }

    .image-slider-sidebar {
        min-height: 300px;

        .slider-wrapper {
            height: 300px;
        }

        .slide-overlay {
            bottom: 30px;
            left: 20px;
            right: 20px;

            .slide-title {
                font-size: 20px;
            }

            .slide-subtitle {
                font-size: 12px;
            }
        }

        .slide-indicators {
            bottom: 15px;

            .indicator {
                width: 10px;
                height: 10px;
            }
        }
    }
}