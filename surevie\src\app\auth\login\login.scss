// Vermeg-inspired color palette
$primary-red: #e31e24;
$dark-gray: #333333;
$light-gray: #f8f9fa;
$white: #ffffff;
$border-gray: #e0e0e0;
$error-red: #dc3545;
$success-green: #28a745;

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;

    .login-card {
        background: $white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;

        .login-header {
            text-align: center;
            margin-bottom: 30px;

            .login-title {
                font-size: 28px;
                font-weight: 700;
                color: $dark-gray;
                margin: 0 0 10px 0;
            }

            .login-subtitle {
                font-size: 16px;
                color: #666;
                margin: 0;
            }
        }

        .login-form {
            .form-group {
                margin-bottom: 20px;

                &.checkbox-group {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 25px;
                }

                .form-label {
                    display: block;
                    font-weight: 600;
                    color: $dark-gray;
                    margin-bottom: 8px;
                    font-size: 14px;
                }

                .form-input {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid $border-gray;
                    border-radius: 8px;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    box-sizing: border-box;

                    &:focus {
                        outline: none;
                        border-color: $primary-red;
                        box-shadow: 0 0 0 3px rgba(227, 30, 36, 0.1);
                    }

                    &.error {
                        border-color: $error-red;

                        &:focus {
                            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
                        }
                    }

                    &::placeholder {
                        color: #999;
                    }
                }

                .password-input-container {
                    position: relative;

                    .password-toggle {
                        position: absolute;
                        right: 12px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: none;
                        border: none;
                        cursor: pointer;
                        padding: 4px;

                        .password-icon {
                            font-size: 18px;
                            color: #666;
                        }
                    }
                }

                .checkbox-label {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    font-size: 14px;
                    color: $dark-gray;

                    .checkbox-input {
                        display: none;

                        &:checked+.checkbox-custom {
                            background-color: $primary-red;
                            border-color: $primary-red;

                            &::after {
                                opacity: 1;
                            }
                        }
                    }

                    .checkbox-custom {
                        width: 18px;
                        height: 18px;
                        border: 2px solid $border-gray;
                        border-radius: 3px;
                        margin-right: 8px;
                        position: relative;
                        transition: all 0.3s ease;

                        &::after {
                            content: '✓';
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            color: $white;
                            font-size: 12px;
                            opacity: 0;
                            transition: opacity 0.3s ease;
                        }
                    }
                }

                .forgot-password {
                    color: $primary-red;
                    text-decoration: none;
                    font-size: 14px;
                    font-weight: 500;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                .error-message {
                    color: $error-red;
                    font-size: 13px;
                    margin-top: 5px;

                    &.global-error {
                        background-color: #f8d7da;
                        border: 1px solid #f5c6cb;
                        border-radius: 6px;
                        padding: 12px;
                        margin-bottom: 20px;
                        text-align: center;
                    }
                }
            }

            .submit-btn {
                width: 100%;
                background-color: $primary-red;
                color: $white;
                border: none;
                border-radius: 8px;
                padding: 14px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-bottom: 20px;

                &:hover:not(:disabled) {
                    background-color: darken($primary-red, 10%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(227, 30, 36, 0.3);
                }

                &:disabled {
                    background-color: #ccc;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                .loading-spinner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;

                    .spinner {
                        width: 16px;
                        height: 16px;
                        border: 2px solid transparent;
                        border-top: 2px solid $white;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                }
            }

            .demo-credentials {
                background-color: #f8f9fa;
                border: 1px solid $border-gray;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;

                .demo-title {
                    font-weight: 600;
                    color: $dark-gray;
                    margin: 0 0 10px 0;
                    font-size: 14px;
                }

                .demo-accounts {
                    .demo-account {
                        font-size: 13px;
                        color: #666;
                        margin-bottom: 5px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        strong {
                            color: $dark-gray;
                        }
                    }
                }
            }
        }

        .login-footer {
            text-align: center;

            .signup-link {
                color: #666;
                font-size: 14px;
                margin: 0;

                .link {
                    color: $primary-red;
                    text-decoration: none;
                    font-weight: 500;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// Responsive design
@media (max-width: 480px) {
    .login-container {
        padding: 10px;

        .login-card {
            padding: 30px 20px;
        }
    }
}