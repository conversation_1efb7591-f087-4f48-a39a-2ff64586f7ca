import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'broker' | 'client';
  createdAt: Date;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role: 'broker' | 'client';
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  // Mock users for demonstration
  private mockUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      createdAt: new Date()
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: '<PERSON>roker',
      role: 'broker',
      createdAt: new Date()
    }
  ];

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    // Check if user is already logged in (from localStorage)
    this.checkStoredAuth();
  }

  private checkStoredAuth(): void {
    // Only access localStorage in the browser
    if (isPlatformBrowser(this.platformId)) {
      const storedUser = localStorage.getItem('currentUser');
      const storedToken = localStorage.getItem('authToken');

      if (storedUser && storedToken) {
        const user = JSON.parse(storedUser);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
      }
    }
  }

  login(credentials: LoginCredentials): Observable<{ success: boolean; user?: User; error?: string }> {
    // Simulate API call with delay
    return of(null).pipe(
      delay(1000),
      map(() => {
        // Mock authentication logic
        const user = this.mockUsers.find(u => u.email === credentials.email);

        if (user && credentials.password === 'password123') {
          // Store user data (only in browser)
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.setItem('authToken', 'mock-jwt-token');
          }

          this.currentUserSubject.next(user);
          this.isAuthenticatedSubject.next(true);

          return { success: true, user };
        } else {
          return { success: false, error: 'Invalid email or password' };
        }
      })
    );
  }

  register(userData: RegisterData): Observable<{ success: boolean; user?: User; error?: string }> {
    // Simulate API call with delay
    return of(null).pipe(
      delay(1000),
      map(() => {
        // Check if user already exists
        const existingUser = this.mockUsers.find(u => u.email === userData.email);

        if (existingUser) {
          return { success: false, error: 'User with this email already exists' };
        }

        // Create new user
        const newUser: User = {
          id: Date.now().toString(),
          email: userData.email,
          name: userData.name,
          role: userData.role,
          createdAt: new Date()
        };

        this.mockUsers.push(newUser);

        // Store user data (only in browser)
        if (isPlatformBrowser(this.platformId)) {
          localStorage.setItem('currentUser', JSON.stringify(newUser));
          localStorage.setItem('authToken', 'mock-jwt-token');
        }

        this.currentUserSubject.next(newUser);
        this.isAuthenticatedSubject.next(true);

        return { success: true, user: newUser };
      })
    );
  }

  logout(): void {
    // Remove user data (only in browser)
    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem('currentUser');
      localStorage.removeItem('authToken');
    }

    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user ? user.role === role : false;
  }
}
