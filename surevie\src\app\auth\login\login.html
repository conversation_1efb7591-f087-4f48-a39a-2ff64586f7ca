<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1 class="login-title">Welcome Back</h1>
            <p class="login-subtitle">Sign in to your SureVie account</p>
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" formControlName="email" class="form-input"
                    [class.error]="isFieldInvalid('email')" placeholder="Enter your email" />
                <div class="error-message" *ngIf="isFieldInvalid('email')">
                    <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
                    <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
                </div>
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <div class="password-input-container">
                    <input [type]="showPassword ? 'text' : 'password'" id="password" formControlName="password"
                        class="form-input" [class.error]="isFieldInvalid('password')"
                        placeholder="Enter your password" />
                    <button type="button" class="password-toggle" (click)="togglePasswordVisibility()">
                        <span class="password-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</span>
                    </button>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('password')">
                    <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
                    <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6
                        characters</span>
                </div>
            </div>

            <!-- Remember Me -->
            <div class="form-group checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" formControlName="rememberMe" class="checkbox-input">
                    <span class="checkbox-custom"></span>
                    Remember me
                </label>
                <a href="#" class="forgot-password">Forgot password?</a>
            </div>

            <!-- Error Message -->
            <div class="error-message global-error" *ngIf="errorMessage">
                {{ errorMessage }}
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn" [disabled]="loginForm.invalid || isLoading">
                <span *ngIf="!isLoading">Sign In</span>
                <span *ngIf="isLoading" class="loading-spinner">
                    <span class="spinner"></span>
                    Signing in...
                </span>
            </button>

            <!-- Demo Credentials -->
            <div class="demo-credentials">
                <p class="demo-title">Demo Credentials:</p>
                <div class="demo-accounts">
                    <div class="demo-account">
                        <strong>Admin:</strong> admin&#64;surevie.com / password123
                    </div>
                    <div class="demo-account">
                        <strong>Broker:</strong> broker&#64;surevie.com / password123
                    </div>
                </div>
            </div>
        </form>

        <div class="login-footer">
            <p class="signup-link">
                Don't have an account?
                <a (click)="navigateToRegister()" class="link">Sign up here</a>
            </p>
        </div>
    </div>
</div>