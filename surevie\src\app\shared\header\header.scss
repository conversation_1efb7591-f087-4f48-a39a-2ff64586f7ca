// Vermeg-inspired color palette
$primary-red: #e31e24;
$dark-gray: #333333;
$light-gray: #f8f9fa;
$white: #ffffff;
$border-gray: #e0e0e0;

.header {
  background-color: $white;
  border-bottom: 1px solid $border-gray;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
  }

  .logo {
    .logo-link {
      text-decoration: none;
      color: $dark-gray;

      .logo-text {
        font-size: 28px;
        font-weight: bold;
        color: $primary-red;
        letter-spacing: -0.5px;
      }
    }
  }

  .nav-menu {
    display: none;

    @media (min-width: 768px) {
      display: block;
    }

    .nav-list {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 30px;

      .nav-item {
        .nav-link {
          text-decoration: none;
          color: $dark-gray;
          font-weight: 500;
          font-size: 16px;
          padding: 10px 0;
          transition: color 0.3s ease;
          position: relative;

          &:hover {
            color: $primary-red;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: $primary-red;
            transition: width 0.3s ease;
          }

          &:hover::after {
            width: 100%;
          }
        }
      }
    }
  }

  .auth-actions {
    display: none;

    @media (min-width: 768px) {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .user-menu {
      display: flex;
      align-items: center;
      gap: 15px;

      .user-name {
        color: $dark-gray;
        font-weight: 500;
      }
    }
  }

  .btn {
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-decoration: none;
    display: inline-block;
    text-align: center;

    &.btn-outline {
      background-color: transparent;
      color: $dark-gray;
      border-color: $border-gray;

      &:hover {
        background-color: $light-gray;
        border-color: $dark-gray;
      }
    }

    &.btn-primary {
      background-color: $primary-red;
      color: $white;
      border-color: $primary-red;

      &:hover {
        background-color: darken($primary-red, 10%);
        border-color: darken($primary-red, 10%);
      }
    }
  }

  .mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 25px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;

    @media (min-width: 768px) {
      display: none;
    }

    .hamburger-line {
      width: 100%;
      height: 3px;
      background-color: $dark-gray;
      transition: all 0.3s ease;
    }
  }

  .mobile-menu {
    display: none;
    background-color: $white;
    border-top: 1px solid $border-gray;
    padding: 20px 0;

    &.active {
      display: block;
    }

    @media (min-width: 768px) {
      display: none !important;
    }

    .mobile-nav-list {
      list-style: none;
      margin: 0;
      padding: 0;

      .mobile-nav-item {
        padding: 10px 0;
        border-bottom: 1px solid $border-gray;

        &:last-child {
          border-bottom: none;
        }

        &.auth-mobile {
          padding: 20px 0 10px;
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .mobile-nav-link {
          text-decoration: none;
          color: $dark-gray;
          font-weight: 500;
          font-size: 16px;
          display: block;
          padding: 10px 0;

          &:hover {
            color: $primary-red;
          }
        }

        .mobile-btn {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }
  }
}