// Import the same styles as login with some modifications
// Vermeg-inspired color palette
$primary-red: #e31e24;
$dark-gray: #333333;
$light-gray: #f8f9fa;
$white: #ffffff;
$border-gray: #e0e0e0;
$error-red: #dc3545;
$success-green: #28a745;

.register-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;

    .register-card {
        background: $white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 500px;

        .register-header {
            text-align: center;
            margin-bottom: 30px;

            .register-title {
                font-size: 28px;
                font-weight: 700;
                color: $dark-gray;
                margin: 0 0 10px 0;
            }

            .register-subtitle {
                font-size: 16px;
                color: #666;
                margin: 0;
            }
        }

        .register-form {
            .form-group {
                margin-bottom: 20px;

                &.checkbox-group {
                    margin-bottom: 25px;

                    .checkbox-label {
                        display: flex;
                        align-items: flex-start;
                        cursor: pointer;
                        font-size: 14px;
                        color: $dark-gray;
                        line-height: 1.4;

                        .checkbox-input {
                            display: none;

                            &:checked+.checkbox-custom {
                                background-color: $primary-red;
                                border-color: $primary-red;

                                &::after {
                                    opacity: 1;
                                }
                            }
                        }

                        .checkbox-custom {
                            width: 18px;
                            height: 18px;
                            border: 2px solid $border-gray;
                            border-radius: 3px;
                            margin-right: 8px;
                            margin-top: 2px;
                            position: relative;
                            transition: all 0.3s ease;
                            flex-shrink: 0;

                            &::after {
                                content: '✓';
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                color: $white;
                                font-size: 12px;
                                opacity: 0;
                                transition: opacity 0.3s ease;
                            }
                        }

                        .link {
                            color: $primary-red;
                            text-decoration: none;
                            font-weight: 500;

                            &:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }

                .form-label {
                    display: block;
                    font-weight: 600;
                    color: $dark-gray;
                    margin-bottom: 8px;
                    font-size: 14px;
                }

                .form-input {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid $border-gray;
                    border-radius: 8px;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    box-sizing: border-box;

                    &:focus {
                        outline: none;
                        border-color: $primary-red;
                        box-shadow: 0 0 0 3px rgba(227, 30, 36, 0.1);
                    }

                    &.error {
                        border-color: $error-red;

                        &:focus {
                            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
                        }
                    }

                    &::placeholder {
                        color: #999;
                    }

                    &.form-select {
                        cursor: pointer;
                        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                        background-position: right 12px center;
                        background-repeat: no-repeat;
                        background-size: 16px;
                        padding-right: 40px;
                        appearance: none;
                    }
                }

                .password-input-container {
                    position: relative;

                    .password-toggle {
                        position: absolute;
                        right: 12px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: none;
                        border: none;
                        cursor: pointer;
                        padding: 4px;

                        .password-icon {
                            font-size: 18px;
                            color: #666;
                        }
                    }
                }

                .error-message {
                    color: $error-red;
                    font-size: 13px;
                    margin-top: 5px;

                    &.global-error {
                        background-color: #f8d7da;
                        border: 1px solid #f5c6cb;
                        border-radius: 6px;
                        padding: 12px;
                        margin-bottom: 20px;
                        text-align: center;
                    }
                }
            }

            .submit-btn {
                width: 100%;
                background-color: $primary-red;
                color: $white;
                border: none;
                border-radius: 8px;
                padding: 14px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-bottom: 20px;

                &:hover:not(:disabled) {
                    background-color: darken($primary-red, 10%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(227, 30, 36, 0.3);
                }

                &:disabled {
                    background-color: #ccc;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                .loading-spinner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;

                    .spinner {
                        width: 16px;
                        height: 16px;
                        border: 2px solid transparent;
                        border-top: 2px solid $white;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                }
            }
        }

        .register-footer {
            text-align: center;

            .login-link {
                color: #666;
                font-size: 14px;
                margin: 0;

                .link {
                    color: $primary-red;
                    text-decoration: none;
                    font-weight: 500;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// Responsive design
@media (max-width: 480px) {
    .register-container {
        padding: 10px;

        .register-card {
            padding: 30px 20px;
        }
    }
}