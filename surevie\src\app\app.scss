/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

/* Vermeg Navigation Header Styles */
.vermeg-header {
    background-color: #ffffff;
    color: #333333;
    padding: 0;
    position: relative;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #f0f0f0;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.header-right {
    position: absolute;
    top: 20px;
    right: 30px;
    display: flex;
    align-items: center;
    gap: 25px;
    z-index: 1001;
}

.search-btn {
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background-color: #f5f5f5;
    color: #e91e63;
}

.language-selector {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.5px;
}

.main-nav {
    display: flex;
    align-items: center;
    padding: 20px 30px;
}

.logo {
    margin-right: 50px;
}

.logo-text {
    font-size: 28px;
    font-weight: 700;
    color: #333333;
    font-family: 'Poppins', sans-serif;
    letter-spacing: -0.5px;
}

.logo-text::before {
    content: '/';
    color: #e91e63;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 40px;
    flex: 1;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #333333;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    padding: 12px 0;
    transition: all 0.3s ease;
    display: block;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.2px;
    position: relative;
}

.nav-link:hover {
    color: #e91e63;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #e91e63;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.contact-btn {
    background: linear-gradient(135deg, #e91e63, #f06292);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.3px;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
    text-transform: uppercase;
}

.contact-btn:hover {
    background: linear-gradient(135deg, #c2185b, #e91e63);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-right {
        position: static;
        margin-bottom: 10px;
        justify-content: flex-end;
    }

    .main-nav {
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-menu {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        width: 100%;
    }

    .logo {
        margin-right: 0;
        margin-bottom: 20px;
    }
}