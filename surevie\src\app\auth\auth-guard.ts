import { Injectable, inject } from '@angular/core';
import { CanActivateFn, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { map } from 'rxjs';
import { AuthService } from './auth';

// Functional guard approach (Angular 15+)
export const authGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  return authService.isAuthenticated$.pipe(
    map(isAuthenticated => {
      if (isAuthenticated) {
        // Check if user has permission for the requested route
        const currentUser = authService.getCurrentUser();
        const requestedPath = state.url;

        if (currentUser) {
          // Admin can access admin routes
          if (requestedPath.includes('/admin') && currentUser.role === 'admin') {
            return true;
          }

          // Broker can access broker routes
          if (requestedPath.includes('/broker') && currentUser.role === 'broker') {
            return true;
          }

          // Client can access client routes
          if (requestedPath.includes('/client') && currentUser.role === 'client') {
            return true;
          }

          // Redirect to appropriate dashboard if accessing wrong role route
          redirectToRoleDashboard(currentUser.role, router);
          return false;
        }
      }

      // Not authenticated, redirect to login
      router.navigate(['/auth/login'], {
        queryParams: { returnUrl: state.url }
      });
      return false;
    })
  );
};

function redirectToRoleDashboard(role: string, router: Router): void {
  switch (role) {
    case 'admin':
      router.navigate(['/admin/dashboard']);
      break;
    case 'broker':
      router.navigate(['/broker/dashboard']);
      break;
    case 'client':
      router.navigate(['/client/dashboard']);
      break;
    default:
      router.navigate(['/auth/login']);
  }
}

// Class-based guard for compatibility
@Injectable({
  providedIn: 'root'
})
export class AuthGuard {
  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ) {
    return authGuard(route, state);
  }
}
